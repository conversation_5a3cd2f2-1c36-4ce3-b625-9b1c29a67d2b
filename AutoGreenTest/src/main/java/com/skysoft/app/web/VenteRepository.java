package com.skysoft.app.web;

import com.skysoft.app.model.VenteDto;
import com.skysoft.app.web.config.ApplicationProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class VenteRepository {
	private final WebClient webClient;
	private final ApplicationProperties applicationProperties;
	private final AuthenticationRepository authenticationRepository;

	public VenteDto createVente(VenteDto venteDto) {
		return webClient
				.post()
				.uri(applicationProperties.getJwt().getUrlRoot() + "/ventes")
				.header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
				.header(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON.toString())
				.header(HttpHeaders.AUTHORIZATION, "Bearer " + authenticationRepository.getToken())
				.body(Mono.just(venteDto), VenteDto.class)
				.retrieve()
				.bodyToMono(VenteDto.class)
				.doOnSuccess(
						vente ->
								log.info(
										"Vente: {}, {}", vente.getId(), "POST/ventes request successful"))
				.doOnError(throwable -> log.error("Error while creating vente", throwable))
				.block();
	}
}
