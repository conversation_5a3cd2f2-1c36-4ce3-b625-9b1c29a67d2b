Feature: Validation de vente
  En tant que entreprise
  Je veux valider les données d'une vente avant sa création

  Scenario Outline: 1.0 validation des champs obligatoires d'une vente
    Given les informations de la vente à "valider" avec "<referenceIdContexte>" sont
      | code   | commentaire   | articleCode   | articleDesignation   | quantite   | prixUnitaire   |
      | <code> | <commentaire> | <articleCode> | <articleDesignation> | <quantite> | <prixUnitaire> |
    When "valider" la vente avec "<referenceIdContexte>"
    Then la vente a des erreurs de validation avec "<referenceIdContexte>"
      | messageErreur   |
      | <messageErreur> |

    Examples:
      | referenceIdContexte           | code   | commentaire        | articleCode | articleDesignation | quantite | prixUnitaire | messageErreur                                    |
      | validate-vente-scenario-1.0   |        | Vente test         | ART001      | Ordinateur Dell    | 1        | 500.00       | Veuillez renseigner le code de la vente          |
      | validate-vente-scenario-1.1   | VTE001 | Vente test         |             | Ordinateur Dell    | 1        | 500.00       | Veuillez renseigner le code de l'article         |
      | validate-vente-scenario-1.2   | VTE001 | Vente test         | ART001      |                    | 1        | 500.00       | Veuillez renseigner la designation de l'article  |
      | validate-vente-scenario-1.3   | VTE001 | Vente test         | ART001      | Ordinateur Dell    |          | 500.00       | Veuillez renseigner la quantite                  |
      | validate-vente-scenario-1.4   | VTE001 | Vente test         | ART001      | Ordinateur Dell    | 1        |              | Veuillez renseigner le prix unitaire             |
