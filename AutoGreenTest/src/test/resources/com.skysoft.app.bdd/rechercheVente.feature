Feature: Recherche de vente
  En tant que entreprise
  Je veux rechercher des ventes

  Scenario Outline: 1.0 recherche d'une vente par code
    Given les informations de la vente à "rechercher" avec "<referenceIdContexte>" sont
      | code   | commentaire   | articleCode   | articleDesignation   | quantite   | prixUnitaire   |
      | <code> | <commentaire> | <articleCode> | <articleDesignation> | <quantite> | <prixUnitaire> |
    And "creation" de la vente avec "<referenceIdContexte>"
      | rien |
      |      |
    When "rechercher" la vente avec "<referenceIdContexte>"
    Then la vente est "trouvée" avec "<referenceIdContexte>"
      | code   | commentaire   | articleCode   | articleDesignation   | quantite   | prixUnitaire   |
      | <code> | <commentaire> | <articleCode> | <articleDesignation> | <quantite> | <prixUnitaire> |

    Examples:
      | referenceIdContexte           | code    | commentaire           | articleCode | articleDesignation | quantite | prixUnitaire |
      | search-vente-scenario-1.0     | VTE300  | Vente pour recherche  | ART001      | Ordinateur Dell    | 1        | 500.00       |
      | search-vente-scenario-1.1     | VTE301  | Test recherche        | ART002      | Souris Logitech    | 3        | 25.00        |

  Scenario Outline: 2.0 recherche d'une vente inexistante
    When "rechercher" la vente inexistante avec "<referenceIdContexte>"
    Then aucune vente n'est trouvée avec "<referenceIdContexte>"

    Examples:
      | referenceIdContexte               |
      | search-vente-notfound-scenario-2.0|
      | search-vente-notfound-scenario-2.1|
