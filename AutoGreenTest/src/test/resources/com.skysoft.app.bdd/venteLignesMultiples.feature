Feature: Vente avec lignes multiples
  En tant que entreprise
  Je veux créer une vente avec plusieurs lignes d'articles

  Scenario Outline: 1.0 création d'une vente avec plusieurs articles
    Given les informations de la vente à "créer" avec "<referenceIdContexte>" sont
      | code   | commentaire   | articleCode   | articleDesignation   | quantite   | prixUnitaire   |
      | <code> | <commentaire> | <articleCode> | <articleDesignation> | <quantite> | <prixUnitaire> |
    When "créer" la vente avec "<referenceIdContexte>"
    Then la vente est "créée" avec "<referenceIdContexte>"
      | code   | commentaire   | articleCode   | articleDesignation   | quantite   | prixUnitaire   |
      | <code> | <commentaire> | <articleCode> | <articleDesignation> | <quantite> | <prixUnitaire> |

    Examples:
      | referenceIdContexte               | code    | commentaire                    | articleCode | articleDesignation    | quantite | prixUnitaire |
      | create-vente-multiple-scenario-1.0| VTE100  | Vente pack bureau complet      | ART001      | Ordinateur Dell       | 1        | 500.00       |
      | create-vente-multiple-scenario-1.1| VTE101  | Vente accessoires gaming       | ART002      | Souris Gaming         | 1        | 45.00        |
      | create-vente-multiple-scenario-1.2| VTE102  | Vente matériel professionnel   | ART003      | Écran 24 pouces       | 2        | 200.00       |
