Feature: Validation de mouvement de stock
  En tant que entreprise
  Je veux valider les données d'un mouvement de stock avant sa création

  Scenario Outline: 1.0 validation des champs obligatoires d'un mouvement de stock
    Given les informations du mouvement de stock à "valider" avec "<referenceIdContexte>" sont
      | quantite   | articleId   | articleCode   | articleDesignation   | typeMvtStk   | sourceMvtStk   |
      | <quantite> | <articleId> | <articleCode> | <articleDesignation> | <typeMvtStk> | <sourceMvtStk> |
    When "valider" le mouvement de stock avec "<referenceIdContexte>"
    Then le mouvement de stock a des erreurs de validation avec "<referenceIdContexte>"
      | messageErreur   |
      | <messageErreur> |

    Examples:
      | referenceIdContexte                    | quantite | articleId | articleCode | articleDesignation | typeMvtStk | sourceMvtStk        | messageErreur                                        |
      | validate-mouvement-scenario-1.0        |          | 1         | ART001      | Ordinateur Dell    | ENTREE     | COMMANDE_FOURNISSEUR| Veuillez renseigner la quantite du mouvement        |
      | validate-mouvement-scenario-1.1        | 10       |           | ART001      | Ordinateur Dell    | ENTREE     | COMMANDE_FOURNISSEUR| Veuillez renseigner l'article du mouvement          |
      | validate-mouvement-scenario-1.2        | 10       | 1         |             | Ordinateur Dell    | ENTREE     | COMMANDE_FOURNISSEUR| Veuillez renseigner le code de l'article             |
      | validate-mouvement-scenario-1.3        | 10       | 1         | ART001      |                    | ENTREE     | COMMANDE_FOURNISSEUR| Veuillez renseigner la designation de l'article     |
      | validate-mouvement-scenario-1.4        | 0        | 1         | ART001      | Ordinateur Dell    | ENTREE     | COMMANDE_FOURNISSEUR| La quantite doit être supérieure à zéro             |
