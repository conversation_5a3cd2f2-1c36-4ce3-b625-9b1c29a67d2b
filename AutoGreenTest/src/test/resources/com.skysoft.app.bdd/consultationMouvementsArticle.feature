Feature: Consultation des mouvements d'un article
  En tant que entreprise
  Je veux consulter l'historique des mouvements de stock d'un article

  Scenario Outline: 1.0 consultation des mouvements d'un article sans historique
    Given un article avec l'id "<articleId>" existe
    When je consulte les mouvements de l'article avec "<referenceIdContexte>"
    Then la liste des mouvements est affichée avec "<referenceIdContexte>"
      | nombreMouvements   |
      | <nombreMouvements> |

    Examples:
      | referenceIdContexte                 | articleId | nombreMouvements |
      | consult-mouvements-scenario-1.0     | 1         | 0                |
      | consult-mouvements-scenario-1.1     | 2         | 0                |

  Scenario Outline: 2.0 consultation des mouvements après création de mouvements
    Given les informations du mouvement de stock à "créer" avec "<referenceIdContexte>" sont
      | quantite   | articleId   | articleCode   | articleDesignation   | typeMvtStk   | sourceMvtStk        |
      | <quantite> | <articleId> | <articleCode> | <articleDesignation> | <typeMvtStk> | <sourceMvtStk>      |
    And "creation" du mouvement de stock avec "<referenceIdContexte>"
      | rien |
      |      |
    When je consulte les mouvements de l'article avec "<referenceIdContexte>"
    Then la liste des mouvements est affichée avec "<referenceIdContexte>"
      | nombreMouvements   |
      | <nombreAttendu>    |
    And le dernier mouvement correspond aux données avec "<referenceIdContexte>"
      | quantite   | typeMvtStk   | sourceMvtStk   |
      | <quantite> | <typeMvtStk> | <sourceMvtStk> |

    Examples:
      | referenceIdContexte                    | quantite | articleId | articleCode | articleDesignation | typeMvtStk | sourceMvtStk        | nombreAttendu |
      | consult-mouvements-after-entree-2.0    | 30       | 1         | ART001      | Ordinateur Dell    | ENTREE     | COMMANDE_FOURNISSEUR| 1             |
      | consult-mouvements-after-sortie-2.0    | 5        | 2         | ART002      | Souris Logitech    | SORTIE     | VENTE               | 1             |
