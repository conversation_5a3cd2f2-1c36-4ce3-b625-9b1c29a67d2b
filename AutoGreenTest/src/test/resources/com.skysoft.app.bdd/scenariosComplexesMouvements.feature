Feature: Scénarios complexes de mouvements de stock
  En tant que entreprise
  Je veux gérer des scénarios complexes avec plusieurs types de mouvements

  Scenario Outline: 1.0 scénario complet : entrée puis sortie
    Given les informations du mouvement de stock à "créer" avec "<referenceIdContexteEntree>" sont
      | quantite   | articleId   | articleCode   | articleDesignation   | typeMvtStk   | sourceMvtStk        |
      | <quantiteEntree> | <articleId> | <articleCode> | <articleDesignation> | ENTREE | COMMANDE_FOURNISSEUR |
    And "creation" du mouvement de stock avec "<referenceIdContexteEntree>"
      | rien |
      |      |
    And les informations du mouvement de stock à "créer" avec "<referenceIdContexteSortie>" sont
      | quantite   | articleId   | articleCode   | articleDesignation   | typeMvtStk   | sourceMvtStk   |
      | <quantiteSortie> | <articleId> | <articleCode> | <articleDesignation> | SORTIE | VENTE |
    When "créer" le mouvement de stock avec "<referenceIdContexteSortie>"
    Then le mouvement de stock est "créé" avec "<referenceIdContexteSortie>"
      | quantite   | articleId   | articleCode   | articleDesignation   | typeMvtStk   | sourceMvtStk   |
      | <quantiteSortie> | <articleId> | <articleCode> | <articleDesignation> | SORTIE | VENTE |
    And je consulte le stock réel de l'article avec "<referenceIdContexteSortie>"
    And le stock réel est affiché avec "<referenceIdContexteSortie>"
      | stockReel      |
      | <stockFinal>   |

    Examples:
      | referenceIdContexteEntree    | referenceIdContexteSortie    | quantiteEntree | quantiteSortie | articleId | articleCode | articleDesignation | stockFinal |
      | complex-entree-scenario-1.0  | complex-sortie-scenario-1.0  | 100            | 30             | 1         | ART001      | Ordinateur Dell    | 70         |
      | complex-entree-scenario-1.1  | complex-sortie-scenario-1.1  | 50             | 50             | 2         | ART002      | Souris Logitech    | 0          |

  Scenario Outline: 2.0 scénario avec corrections
    Given les informations du mouvement de stock à "créer" avec "<referenceIdContexteEntree>" sont
      | quantite   | articleId   | articleCode   | articleDesignation   | typeMvtStk   | sourceMvtStk        |
      | <quantiteEntree> | <articleId> | <articleCode> | <articleDesignation> | ENTREE | COMMANDE_FOURNISSEUR |
    And "creation" du mouvement de stock avec "<referenceIdContexteEntree>"
      | rien |
      |      |
    And les informations du mouvement de stock à "créer" avec "<referenceIdContexteCorrection>" sont
      | quantite   | articleId   | articleCode   | articleDesignation   | typeMvtStk      | sourceMvtStk        |
      | <quantiteCorrection> | <articleId> | <articleCode> | <articleDesignation> | <typeCorrection> | COMMANDE_FOURNISSEUR |
    When "créer" le mouvement de stock avec "<referenceIdContexteCorrection>"
    Then le mouvement de stock est "créé" avec "<referenceIdContexteCorrection>"
      | quantite   | articleId   | articleCode   | articleDesignation   | typeMvtStk      | sourceMvtStk        |
      | <quantiteCorrection> | <articleId> | <articleCode> | <articleDesignation> | <typeCorrection> | COMMANDE_FOURNISSEUR |
    And je consulte le stock réel de l'article avec "<referenceIdContexteCorrection>"
    And le stock réel est affiché avec "<referenceIdContexteCorrection>"
      | stockReel      |
      | <stockFinal>   |

    Examples:
      | referenceIdContexteEntree      | referenceIdContexteCorrection    | quantiteEntree | quantiteCorrection | typeCorrection | articleId | articleCode | articleDesignation | stockFinal |
      | complex-correction-entree-2.0  | complex-correction-pos-2.0       | 80             | 20                 | CORRECTION_POS | 3         | ART003      | Clavier mécanique  | 100        |
      | complex-correction-entree-2.1  | complex-correction-neg-2.1       | 60             | 10                 | CORRECTION_NEG | 4         | ART004      | Écran 24 pouces    | 50         |
