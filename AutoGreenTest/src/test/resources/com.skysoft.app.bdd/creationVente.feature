Feature: Creation de vente
  En tant que entreprise
  Je veux créer une vente

  Scenario Outline: 1.0 création d'une vente
    Given les informations de la vente à "créer" avec "<referenceIdContexte>" sont
      | code   | commentaire   | articleCode   | articleDesignation   | quantite   | prixUnitaire   |
      | <code> | <commentaire> | <articleCode> | <articleDesignation> | <quantite> | <prixUnitaire> |
    When "créer" la vente avec "<referenceIdContexte>"
    Then la vente est "créée" avec "<referenceIdContexte>"
      | code   | commentaire   | articleCode   | articleDesignation   | quantite   | prixUnitaire   |
      | <code> | <commentaire> | <articleCode> | <articleDesignation> | <quantite> | <prixUnitaire> |

    Examples:
      | referenceIdContexte        | code    | commentaire                | articleCode | articleDesignation | quantite | prixUnitaire |
      | create-vente-scenario-1.0  | random  | random                     | random      | random             | random   | random       |
      | create-vente-scenario-1.1  | VTE001  | Vente ordinateur bureau    | ART001      | Ordinateur Dell    | 1        | 500.00       |
      | create-vente-scenario-1.2  | VTE002  | Vente accessoires          | ART002      | Souris Logitech    | 3        | 25.00        |
      | create-vente-scenario-1.3  | VTE003  | Vente matériel informatique| ART003      | Clavier mécanique  | 2        | 75.00        |
