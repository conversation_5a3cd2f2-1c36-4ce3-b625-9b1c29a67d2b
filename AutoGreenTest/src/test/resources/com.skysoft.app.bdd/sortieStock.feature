Feature: Sortie de stock
  En tant que entreprise
  Je veux enregistrer une sortie de stock

  Scenario Outline: 1.0 création d'une sortie de stock
    Given les informations du mouvement de stock à "créer" avec "<referenceIdContexte>" sont
      | quantite   | articleId   | articleCode   | articleDesignation   | typeMvtStk   | sourceMvtStk   |
      | <quantite> | <articleId> | <articleCode> | <articleDesignation> | <typeMvtStk> | <sourceMvtStk> |
    When "créer" le mouvement de stock avec "<referenceIdContexte>"
    Then le mouvement de stock est "créé" avec "<referenceIdContexte>"
      | quantite   | articleId   | articleCode   | articleDesignation   | typeMvtStk   | sourceMvtStk   |
      | <quantite> | <articleId> | <articleCode> | <articleDesignation> | <typeMvtStk> | <sourceMvtStk> |

    Examples:
      | referenceIdContexte           | quantite | articleId | articleCode | articleDesignation | typeMvtStk | sourceMvtStk    |
      | create-sortie-scenario-1.0    | random   | random    | random      | random             | SORTIE     | VENTE           |
      | create-sortie-scenario-1.1    | 5        | 1         | ART001      | Ordinateur Dell    | SORTIE     | VENTE           |
      | create-sortie-scenario-1.2    | 10       | 2         | ART002      | Souris Logitech    | SORTIE     | VENTE           |
      | create-sortie-scenario-1.3    | 2        | 3         | ART003      | Clavier mécanique  | SORTIE     | COMMANDE_CLIENT |
