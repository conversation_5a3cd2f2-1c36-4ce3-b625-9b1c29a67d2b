Feature: Consultation du stock réel
  En tant que entreprise
  Je veux consulter le stock réel d'un article

  Scenario Outline: 1.0 consultation du stock réel d'un article
    Given un article avec l'id "<articleId>" existe
    When je consulte le stock réel de l'article avec "<referenceIdContexte>"
    Then le stock réel est affiché avec "<referenceIdContexte>"
      | stockReel   |
      | <stockReel> |

    Examples:
      | referenceIdContexte              | articleId | stockReel |
      | consult-stock-scenario-1.0       | 1         | 0         |
      | consult-stock-scenario-1.1       | 2         | 0         |
      | consult-stock-scenario-1.2       | 3         | 0         |

  Scenario Outline: 2.0 consultation du stock réel après mouvements
    Given les informations du mouvement de stock à "créer" avec "<referenceIdContexte>" sont
      | quantite   | articleId   | articleCode   | articleDesignation   | typeMvtStk   | sourceMvtStk        |
      | <quantite> | <articleId> | <articleCode> | <articleDesignation> | <typeMvtStk> | <sourceMvtStk>      |
    And "creation" du mouvement de stock avec "<referenceIdContexte>"
      | rien |
      |      |
    When je consulte le stock réel de l'article avec "<referenceIdContexte>"
    Then le stock réel est affiché avec "<referenceIdContexte>"
      | stockReel      |
      | <stockAttendu> |

    Examples:
      | referenceIdContexte                 | quantite | articleId | articleCode | articleDesignation | typeMvtStk | sourceMvtStk        | stockAttendu |
      | consult-stock-after-entree-1.0      | 50       | 1         | ART001      | Ordinateur Dell    | ENTREE     | COMMANDE_FOURNISSEUR| 50           |
      | consult-stock-after-sortie-1.0      | 10       | 1         | ART001      | Ordinateur Dell    | SORTIE     | VENTE               | -10          |
