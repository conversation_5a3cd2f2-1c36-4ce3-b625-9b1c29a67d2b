package com.skysoft.app.bdd;

import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.javafaker.Faker;
import com.skysoft.app.AutoGreenTestApplication;
import com.skysoft.app.model.AdresseDto;
import com.skysoft.app.model.ArticleDto;
import com.skysoft.app.model.CategorieDto;
import com.skysoft.app.model.ClientDto;
import com.skysoft.app.model.EntrepriseDto;
import com.skysoft.app.model.FournisseurDto;
import com.skysoft.app.web.AccountManagerRepository;
import com.skysoft.app.web.ArticleRepository;
import com.skysoft.app.web.AuthenticationRepository;
import com.skysoft.app.web.CategorieRepository;
import com.skysoft.app.web.ClientRepository;
import com.skysoft.app.web.EntrepriseRepository;
import com.skysoft.app.web.FournisseurRepository;
import io.cucumber.spring.CucumberContextConfiguration;
import java.math.BigDecimal;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.web.reactive.function.client.WebClient;

@CucumberContextConfiguration
@SpringBootTest(
    classes = AutoGreenTestApplication.class,
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestPropertySource("classpath:application.properties")
@AutoConfigureMockMvc
@Slf4j
public class AbstractStepDefinition extends ExecutionContext {
  @Autowired protected AccountManagerRepository accountManagerRepository;
  @Autowired protected AuthenticationRepository authenticationRepository;
  @Autowired protected EntrepriseRepository entrepriseRepository;
  @Autowired protected CategorieRepository categorieRepository;
  @Autowired protected ArticleRepository articleRepository;
  @Autowired protected ClientRepository clientRepository;
  @Autowired protected FournisseurRepository fournisseurRepository;
  @Autowired protected MockMvc mockMvc;
  @Autowired protected ObjectMapper objectMapper;
  @Autowired protected WebClient webClient;

  protected EntrepriseDto buildEnreprise(Map<String, String> row) {
    return EntrepriseDto.builder()
        .nom(row.get("nom"))
        .description(row.get("description"))
        .email(row.get("email"))
        .password(row.get("password"))
        .codeFiscal(row.get("codeFiscal"))
        .adresse(
            AdresseDto.builder()
                .adresse1(row.get("adresse1"))
                .adresse2(row.get("adresse2"))
                .ville(row.get("ville"))
                .pays(row.get("pays"))
                .codePostale(row.get("codePostale"))
                .build())
        .numTel(row.get("numTel"))
        .siteWeb(row.get("siteWeb"))
        .build();
  }

  protected void assertEntreprise(EntrepriseDto entrepriseDto, EntrepriseDto entreprise) {
    assert entrepriseDto.getNom().equals(entreprise.getNom());
    assert entrepriseDto.getDescription().equals(entreprise.getDescription());
    assert entrepriseDto.getEmail().equals(entreprise.getEmail());
    assert entrepriseDto.getCodeFiscal().equals(entreprise.getCodeFiscal());
    assert entrepriseDto.getAdresse().getAdresse1().equals(entreprise.getAdresse().getAdresse1());
    assert entrepriseDto.getAdresse().getAdresse2().equals(entreprise.getAdresse().getAdresse2());
    assert entrepriseDto.getAdresse().getVille().equals(entreprise.getAdresse().getVille());
    assert entrepriseDto.getAdresse().getPays().equals(entreprise.getAdresse().getPays());
    assert entrepriseDto
        .getAdresse()
        .getCodePostale()
        .equals(entreprise.getAdresse().getCodePostale());
    assert entrepriseDto.getNumTel().equals(entreprise.getNumTel());
    assert entrepriseDto.getSiteWeb().equals(entreprise.getSiteWeb());
  }

  protected CategorieDto buildCategorie(Map<String, String> row) {
    var fake = new Faker();
    return CategorieDto.builder()
        .code(resolveValue(row, "code", fake.bothify("CAT###")))
        .designation(resolveValue(row, "designation", fake.bothify("Catégorie de l'entreprise")))
        .build();
  }

  private String resolveValue(Map<String, String> row, String code, String randomValue) {
    return row.get(code) != null && row.get(code).equalsIgnoreCase("random")
        ? randomValue
        : row.get(code);
  }

  protected void assertCategorie(CategorieDto categorieCreated, CategorieDto categorie) {
    assert categorieCreated.getCode().equals(categorie.getCode());
    assert categorieCreated.getDesignation().equals(categorie.getDesignation());
    assertNotNull(categorieCreated.getIdEntreprise());
  }

  protected ArticleDto buildArticle(Map<String, String> row) {
    var fake = new Faker();
    return ArticleDto.builder()
        .codeArticle(resolveValue(row, "codeArticle", fake.bothify("ART###")))
        .designation(resolveValue(row, "designation", fake.commerce().productName()))
        .prixUnitaireHt(
            resolveBigDecimalValue(row, "prixUnitaireHt", fake.number().randomDouble(2, 10, 1000)))
        .prixUnitaireTtc(
            resolveBigDecimalValue(row, "prixUnitaireTtc", fake.number().randomDouble(2, 12, 1200)))
        .tauxTva(resolveBigDecimalValue(row, "tauxTva", 19.25))
        .photo(resolveValue(row, "photo", fake.internet().url()))
        .categorie(buildCategorieForArticle(row))
        .build();
  }

  private CategorieDto buildCategorieForArticle(Map<String, String> row) {
    var fake = new Faker();
    return CategorieDto.builder()
        .code(resolveValue(row, "categorieCode", fake.bothify("CAT###")))
        .designation(
            resolveValue(row, "categorieDesignation", fake.bothify("Catégorie de l'entreprise")))
        .build();
  }

  private BigDecimal resolveBigDecimalValue(
      Map<String, String> row, String key, double randomValue) {
    String value = row.get(key);
    if (value != null && value.equalsIgnoreCase("random")) {
      return BigDecimal.valueOf(randomValue);
    } else if (value != null) {
      return new BigDecimal(value);
    }
    return BigDecimal.valueOf(randomValue);
  }

  protected void assertArticle(ArticleDto articleCreated, ArticleDto article) {
    assert articleCreated.getCodeArticle().equals(article.getCodeArticle());
    assert articleCreated.getDesignation().equals(article.getDesignation());
    assert articleCreated.getPrixUnitaireHt().compareTo(article.getPrixUnitaireHt()) == 0;
    assert articleCreated.getPrixUnitaireTtc().compareTo(article.getPrixUnitaireTtc()) == 0;
    assert articleCreated.getTauxTva().compareTo(article.getTauxTva()) == 0;
    assertNotNull(articleCreated.getIdEntreprise());
    if (article.getCategorie() != null) {
      assertNotNull(articleCreated.getCategorie());
      assert articleCreated.getCategorie().getCode().equals(article.getCategorie().getCode());
    }
  }

  protected ClientDto buildClient(Map<String, String> row) {
    var fake = new Faker();
    return ClientDto.builder()
        .nom(resolveValueWithEmpty(row, "nom", fake.name().lastName()))
        .prenom(resolveValueWithEmpty(row, "prenom", fake.name().firstName()))
        .mail(resolveValueWithEmpty(row, "mail", fake.internet().emailAddress()))
        .numTel(resolveValueWithEmpty(row, "numTel", fake.phoneNumber().phoneNumber()))
        .photo(resolveValueWithEmpty(row, "photo", fake.internet().url()))
        .adresse(buildAdresseForClient(row))
        .build();
  }

  private AdresseDto buildAdresseForClient(Map<String, String> row) {
    var fake = new Faker();
    return AdresseDto.builder()
        .adresse1(resolveValueWithEmpty(row, "adresse1", fake.address().streetAddress()))
        .adresse2(resolveValueWithEmpty(row, "adresse2", fake.address().secondaryAddress()))
        .ville(resolveValueWithEmpty(row, "ville", fake.address().city()))
        .pays(resolveValueWithEmpty(row, "pays", fake.address().country()))
        .codePostale(resolveValueWithEmpty(row, "codePostale", fake.address().zipCode()))
        .build();
  }

  protected void assertClient(ClientDto clientCreated, ClientDto client) {
    assert clientCreated.getNom().equals(client.getNom());
    assert clientCreated.getPrenom().equals(client.getPrenom());
    assert clientCreated.getMail().equals(client.getMail());
    assert clientCreated.getNumTel().equals(client.getNumTel());
    assertNotNull(clientCreated.getIdEntreprise());
    if (client.getAdresse() != null) {
      assertNotNull(clientCreated.getAdresse());
      assert clientCreated.getAdresse().getAdresse1().equals(client.getAdresse().getAdresse1());
      assert clientCreated.getAdresse().getVille().equals(client.getAdresse().getVille());
    }
  }

  protected FournisseurDto buildFournisseur(Map<String, String> row) {
    var fake = new Faker();
    return FournisseurDto.builder()
        .nom(resolveValueWithEmpty(row, "nom", fake.name().lastName()))
        .prenom(resolveValueWithEmpty(row, "prenom", fake.name().firstName()))
        .mail(resolveValueWithEmpty(row, "mail", fake.internet().emailAddress()))
        .numTel(resolveValueWithEmpty(row, "numTel", fake.phoneNumber().phoneNumber()))
        .photo(resolveValueWithEmpty(row, "photo", fake.internet().url()))
        .adresse(buildAdresseForFournisseur(row))
        .build();
  }

  private AdresseDto buildAdresseForFournisseur(Map<String, String> row) {
    var fake = new Faker();
    return AdresseDto.builder()
        .adresse1(resolveValueWithEmpty(row, "adresse1", fake.address().streetAddress()))
        .adresse2(resolveValueWithEmpty(row, "adresse2", fake.address().secondaryAddress()))
        .ville(resolveValueWithEmpty(row, "ville", fake.address().city()))
        .pays(resolveValueWithEmpty(row, "pays", fake.address().country()))
        .codePostale(resolveValueWithEmpty(row, "codePostale", fake.address().zipCode()))
        .build();
  }

  protected void assertFournisseur(FournisseurDto fournisseurCreated, FournisseurDto fournisseur) {
    assert fournisseurCreated.getNom().equals(fournisseur.getNom());
    assert fournisseurCreated.getPrenom().equals(fournisseur.getPrenom());
    assert fournisseurCreated.getMail().equals(fournisseur.getMail());
    assert fournisseurCreated.getNumTel().equals(fournisseur.getNumTel());
    assertNotNull(fournisseurCreated.getIdEntreprise());
    if (fournisseur.getAdresse() != null) {
      assertNotNull(fournisseurCreated.getAdresse());
      assert fournisseurCreated.getAdresse().getAdresse1().equals(fournisseur.getAdresse().getAdresse1());
      assert fournisseurCreated.getAdresse().getVille().equals(fournisseur.getAdresse().getVille());
    }
  }
}
