package com.skysoft.app.bdd;

import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.javafaker.Faker;
import com.skysoft.app.AutoGreenTestApplication;
import com.skysoft.app.model.AdresseDto;
import com.skysoft.app.model.CategorieDto;
import com.skysoft.app.model.EntrepriseDto;
import com.skysoft.app.web.AccountManagerRepository;
import com.skysoft.app.web.AuthenticationRepository;
import com.skysoft.app.web.CategorieRepository;
import com.skysoft.app.web.EntrepriseRepository;
import io.cucumber.spring.CucumberContextConfiguration;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.web.reactive.function.client.WebClient;

@CucumberContextConfiguration
@SpringBootTest(
    classes = AutoGreenTestApplication.class,
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestPropertySource("classpath:application.properties")
@AutoConfigureMockMvc
@Slf4j
public class AbstractStepDefinition extends ExecutionContext {
  @Autowired protected AccountManagerRepository accountManagerRepository;
  @Autowired protected AuthenticationRepository authenticationRepository;
  @Autowired protected EntrepriseRepository entrepriseRepository;
  @Autowired protected CategorieRepository categorieRepository;
  @Autowired protected MockMvc mockMvc;
  @Autowired protected ObjectMapper objectMapper;
  @Autowired protected WebClient webClient;

  protected EntrepriseDto buildEnreprise(Map<String, String> row) {
    return EntrepriseDto.builder()
        .nom(row.get("nom"))
        .description(row.get("description"))
        .email(row.get("email"))
        .password(row.get("password"))
        .codeFiscal(row.get("codeFiscal"))
        .adresse(
            AdresseDto.builder()
                .adresse1(row.get("adresse1"))
                .adresse2(row.get("adresse2"))
                .ville(row.get("ville"))
                .pays(row.get("pays"))
                .codePostale(row.get("codePostale"))
                .build())
        .numTel(row.get("numTel"))
        .siteWeb(row.get("siteWeb"))
        .build();
  }

  protected void assertEntreprise(EntrepriseDto entrepriseDto, EntrepriseDto entreprise) {
    assert entrepriseDto.getNom().equals(entreprise.getNom());
    assert entrepriseDto.getDescription().equals(entreprise.getDescription());
    assert entrepriseDto.getEmail().equals(entreprise.getEmail());
    assert entrepriseDto.getCodeFiscal().equals(entreprise.getCodeFiscal());
    assert entrepriseDto.getAdresse().getAdresse1().equals(entreprise.getAdresse().getAdresse1());
    assert entrepriseDto.getAdresse().getAdresse2().equals(entreprise.getAdresse().getAdresse2());
    assert entrepriseDto.getAdresse().getVille().equals(entreprise.getAdresse().getVille());
    assert entrepriseDto.getAdresse().getPays().equals(entreprise.getAdresse().getPays());
    assert entrepriseDto
        .getAdresse()
        .getCodePostale()
        .equals(entreprise.getAdresse().getCodePostale());
    assert entrepriseDto.getNumTel().equals(entreprise.getNumTel());
    assert entrepriseDto.getSiteWeb().equals(entreprise.getSiteWeb());
  }

  protected CategorieDto buildCategorie(Map<String, String> row) {
    var fake = new Faker();
    return CategorieDto.builder()
        .code(resolveValue(row, "code", fake.bothify("CAT###")))
        .designation(resolveValue(row, "designation", fake.bothify("Catégorie de l'entreprise")))
        .build();
  }

  private String resolveValue(Map<String, String> row, String code, String randomValue) {
    return row.get(code) != null && row.get(code).equalsIgnoreCase("random")
        ? randomValue
        : row.get(code);
  }

  protected void assertCategorie(CategorieDto categorieCreated, CategorieDto categorie) {
    assert categorieCreated.getCode().equals(categorie.getCode());
    assert categorieCreated.getDesignation().equals(categorie.getDesignation());
    assertNotNull(categorieCreated.getIdEntreprise());
  }
}
